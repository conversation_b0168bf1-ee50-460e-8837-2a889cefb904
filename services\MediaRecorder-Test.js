/**
 * MediaRecorder 重构后的测试文件
 * 用于验证新的组合模式设计和向后兼容性
 */

// 模拟微信环境
global.wx = {
  getRecorderManager: () => ({
    onStart: () => {},
    onStop: () => {},
    onError: () => {},
    onPause: () => {},
    onResume: () => {},
    onInterruptionBegin: () => {},
    onInterruptionEnd: () => {},
    start: () => {},
    stop: () => {},
    pause: () => {},
    resume: () => {},
    offStart: () => {},
    offStop: () => {},
    offError: () => {},
    offPause: () => {},
    offResume: () => {},
    offInterruptionBegin: () => {},
    offInterruptionEnd: () => {},
  }),
  createCameraContext: () => ({
    startRecord: () => {},
    stopRecord: () => {},
    destroy: () => {},
    _isRecording: false,
  }),
  getSetting: (options) => {
    // 模拟权限已授权
    options.success({
      authSetting: {
        "scope.record": true,
        "scope.camera": true,
      },
    })
  },
  authorize: (options) => {
    // 模拟授权成功
    options.success && options.success()
  },
  openSetting: (options) => {
    options.success && options.success()
  },
}

const MediaRecorder = require("./MediaRecorder")

async function testNewAPI() {
  console.log("=== 测试新的组合模式API ===")

  const mediaRecorder = new MediaRecorder()

  // 测试初始化
  await mediaRecorder.init({
    enableAudio: true,
    enableVideo: true,
    duration: 30000,
  })

  console.log("✓ 初始化成功")

  // 测试独立实例访问
  console.log("音频管理器:", mediaRecorder.audio ? "✓ 存在" : "✗ 不存在")
  console.log("视频管理器:", mediaRecorder.camera ? "✓ 存在" : "✗ 不存在")

  // 测试独立方法调用
  try {
    await mediaRecorder.audio.start("test_question_1")
    console.log("✓ 音频录制启动成功")

    const audioState = mediaRecorder.audio.getState()
    console.log("✓ 音频状态获取成功:", audioState.isRecording)

    await mediaRecorder.camera.start("test_question_1")
    console.log("✓ 视频录制启动成功")

    const cameraState = mediaRecorder.camera.getState()
    console.log("✓ 视频状态获取成功:", cameraState.isRecording)

    // 测试独立停止
    await mediaRecorder.audio.stop()
    console.log("✓ 音频录制停止成功")

    await mediaRecorder.camera.stop()
    console.log("✓ 视频录制停止成功")
  } catch (error) {
    console.log("✗ 新API测试失败:", error.message)
  }
}

async function testBackwardCompatibility() {
  console.log("\n=== 测试向后兼容性 ===")

  const mediaRecorder = new MediaRecorder()

  // 测试原有的初始化方式
  await mediaRecorder.init({
    enableAudio: true,
    enableVideo: true,
    duration: 30000,
    onTimeUpdate: (ms, data) => {
      console.log("✓ 时间更新回调正常")
    },
    onTimeout: () => {
      console.log("✓ 超时回调正常")
    },
    onError: (error) => {
      console.log("✓ 错误回调正常")
    },
  })

  console.log("✓ 向后兼容初始化成功")

  // 测试原有的录制方式
  try {
    await mediaRecorder.start("test_question_2")
    console.log("✓ 向后兼容启动成功")

    const result = await mediaRecorder.stop()
    console.log("✓ 向后兼容停止成功")

    // 测试其他向后兼容方法
    mediaRecorder.resumeRecord()
    console.log("✓ 向后兼容恢复录制成功")

    mediaRecorder.forceStop("test_question_2")
    console.log("✓ 向后兼容强制停止成功")
  } catch (error) {
    console.log("✗ 向后兼容测试失败:", error.message)
  }
}

async function testIndependentControl() {
  console.log("\n=== 测试独立控制功能 ===")

  const mediaRecorder = new MediaRecorder()

  await mediaRecorder.init({
    enableAudio: true,
    enableVideo: true,
  })

  try {
    // 测试独立启动
    await mediaRecorder.audio.start("test_question_3")
    console.log("✓ 独立启动音频成功")

    // 延迟启动视频
    setTimeout(async () => {
      await mediaRecorder.camera.start("test_question_3")
      console.log("✓ 延迟启动视频成功")
    }, 1000)

    // 测试独立暂停恢复
    mediaRecorder.audio.pause()
    console.log("✓ 独立暂停音频成功")

    mediaRecorder.audio.resume()
    console.log("✓ 独立恢复音频成功")

    // 测试状态隔离
    const audioState = mediaRecorder.audio.getState()
    const cameraState = mediaRecorder.camera.getState()

    console.log("✓ 状态隔离验证:", {
      audioRecording: audioState.isRecording,
      cameraRecording: cameraState.isRecording,
    })
  } catch (error) {
    console.log("✗ 独立控制测试失败:", error.message)
  }
}

async function testEventHandling() {
  console.log("\n=== 测试事件处理 ===")

  const mediaRecorder = new MediaRecorder()

  await mediaRecorder.init({
    enableAudio: true,
    enableVideo: true,
  })

  // 设置独立事件回调
  mediaRecorder.audio.onStart = (res) => {
    console.log("✓ 音频开始事件触发")
  }

  mediaRecorder.audio.onStop = (res) => {
    console.log("✓ 音频停止事件触发")
  }

  mediaRecorder.camera.onStart = () => {
    console.log("✓ 视频开始事件触发")
  }

  mediaRecorder.camera.onStop = (res) => {
    console.log("✓ 视频停止事件触发")
  }

  console.log("✓ 事件回调设置成功")
}

async function runAllTests() {
  console.log("开始 MediaRecorder 重构测试...\n")

  try {
    await testNewAPI()
    await testBackwardCompatibility()
    await testIndependentControl()
    await testEventHandling()

    console.log("\n=== 测试总结 ===")
    console.log("✓ 所有测试完成")
    console.log("✓ 新的组合模式API正常工作")
    console.log("✓ 向后兼容性保持良好")
    console.log("✓ 独立控制功能正常")
    console.log("✓ 事件处理机制正常")
  } catch (error) {
    console.log("✗ 测试过程中出现错误:", error)
  }
}

// 运行测试
if (require.main === module) {
  runAllTests()
}

module.exports = {
  testNewAPI,
  testBackwardCompatibility,
  testIndependentControl,
  testEventHandling,
  runAllTests,
}
